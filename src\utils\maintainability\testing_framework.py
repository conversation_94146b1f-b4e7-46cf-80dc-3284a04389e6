#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试框架增强

提供全面的单元测试、集成测试和性能测试支持。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import time
import unittest
import pytest
from typing import Dict, List, Optional, Any, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
import json
import tempfile
import shutil
import os

from ..logger import get_logger
from .enhanced_logging import get_structured_logger

logger = get_logger("TestingFramework")


class TestType(Enum):
    """测试类型枚举"""
    UNIT = "unit"
    INTEGRATION = "integration"
    PERFORMANCE = "performance"
    STRESS = "stress"
    SMOKE = "smoke"
    REGRESSION = "regression"


class TestStatus(Enum):
    """测试状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestResult:
    """测试结果"""
    test_id: str
    test_name: str
    test_type: TestType
    status: TestStatus
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    error_message: Optional[str] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    coverage_data: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self, status: TestStatus, error_message: str = None):
        """完成测试"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = status
        self.error_message = error_message


@dataclass
class TestSuite:
    """测试套件"""
    suite_id: str
    name: str
    description: str
    test_type: TestType
    test_cases: List[str] = field(default_factory=list)
    setup_functions: List[Callable] = field(default_factory=list)
    teardown_functions: List[Callable] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    timeout: float = 300.0  # 5分钟默认超时


class AsyncTestCase(unittest.TestCase):
    """
    异步测试用例基类
    
    提供异步测试的基础设施和工具方法
    """
    
    def setUp(self):
        """设置测试环境"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.test_logger = get_structured_logger(f"test.{self.__class__.__name__}")
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 性能监控
        self.performance_data = {}
        
    def tearDown(self):
        """清理测试环境"""
        try:
            # 清理临时目录
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            
            # 关闭事件循环
            if self.loop and not self.loop.is_closed():
                self.loop.close()
                
        except Exception as e:
            self.test_logger.warning(f"测试清理失败: {e}")
    
    def run_async(self, coro):
        """运行异步协程"""
        return self.loop.run_until_complete(coro)
    
    async def assert_async_raises(self, exception_class, coro):
        """断言异步操作抛出异常"""
        with self.assertRaises(exception_class):
            await coro
    
    async def assert_async_timeout(self, coro, timeout: float):
        """断言异步操作超时"""
        with self.assertRaises(asyncio.TimeoutError):
            await asyncio.wait_for(coro, timeout=timeout)
    
    def measure_performance(self, operation_name: str):
        """性能测量装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start_time
                    self.performance_data[operation_name] = {
                        'duration': duration,
                        'success': True
                    }
                    return result
                except Exception as e:
                    duration = time.time() - start_time
                    self.performance_data[operation_name] = {
                        'duration': duration,
                        'success': False,
                        'error': str(e)
                    }
                    raise
            return wrapper
        return decorator
    
    async def measure_async_performance(self, operation_name: str, coro):
        """测量异步操作性能"""
        start_time = time.time()
        try:
            result = await coro
            duration = time.time() - start_time
            self.performance_data[operation_name] = {
                'duration': duration,
                'success': True
            }
            return result
        except Exception as e:
            duration = time.time() - start_time
            self.performance_data[operation_name] = {
                'duration': duration,
                'success': False,
                'error': str(e)
            }
            raise


class AsyncManagerTestCase(AsyncTestCase):
    """
    异步管理器测试用例
    
    专门用于测试异步管理器功能
    """
    
    def setUp(self):
        """设置异步管理器测试环境"""
        super().setUp()
        
        # 导入异步管理器（避免循环导入）
        from ..async_manager import get_async_manager
        self.async_manager = get_async_manager()
        
        # 测试任务列表
        self.test_tasks = []
    
    def tearDown(self):
        """清理异步管理器测试环境"""
        # 取消所有测试任务
        for task_id in self.test_tasks:
            try:
                self.run_async(self.async_manager.cancel_task(task_id))
            except Exception:
                pass
        
        super().tearDown()
    
    async def submit_test_task(self, func, *args, **kwargs):
        """提交测试任务"""
        task_id = await self.async_manager.submit_task(func, *args, **kwargs)
        self.test_tasks.append(task_id)
        return task_id
    
    async def wait_for_test_task(self, task_id: str, timeout: float = 10.0):
        """等待测试任务完成"""
        return await self.async_manager.wait_for_task(task_id, timeout)
    
    def assert_task_completed(self, task_id: str):
        """断言任务已完成"""
        result = self.run_async(self.async_manager.get_task_result(task_id))
        self.assertIsNotNone(result)
        self.assertEqual(result.status.value, "completed")
    
    def assert_task_failed(self, task_id: str):
        """断言任务失败"""
        result = self.run_async(self.async_manager.get_task_result(task_id))
        self.assertIsNotNone(result)
        self.assertEqual(result.status.value, "failed")


class TestDataGenerator:
    """
    测试数据生成器
    
    生成各种测试场景所需的数据
    """
    
    @staticmethod
    def generate_file_list(count: int, base_path: str = "/test") -> List[str]:
        """生成文件列表"""
        return [f"{base_path}/file_{i:04d}.txt" for i in range(count)]
    
    @staticmethod
    def generate_large_file_list(count: int) -> List[Dict[str, Any]]:
        """生成大文件列表"""
        files = []
        for i in range(count):
            files.append({
                'path': f"/test/large_file_{i:04d}.bin",
                'size': 1024 * 1024 * (i % 100 + 1),  # 1-100MB
                'hash': f"hash_{i:08x}"
            })
        return files
    
    @staticmethod
    def generate_duplicate_files(group_count: int, files_per_group: int) -> List[Dict[str, Any]]:
        """生成重复文件数据"""
        files = []
        for group in range(group_count):
            hash_value = f"duplicate_hash_{group:04d}"
            for file_idx in range(files_per_group):
                files.append({
                    'path': f"/test/group_{group}/file_{file_idx}.txt",
                    'size': 1024 * (group + 1),
                    'hash': hash_value
                })
        return files
    
    @staticmethod
    def create_temp_files(file_list: List[str], base_dir: str) -> List[str]:
        """创建临时测试文件"""
        created_files = []
        for file_path in file_list:
            full_path = os.path.join(base_dir, file_path.lstrip('/'))
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            with open(full_path, 'w') as f:
                f.write(f"Test content for {file_path}")
            
            created_files.append(full_path)
        
        return created_files


class PerformanceTestRunner:
    """
    性能测试运行器
    
    执行性能测试和基准测试
    """
    
    def __init__(self):
        """初始化性能测试运行器"""
        self.logger = get_structured_logger("PerformanceTest")
        self.baseline_data = {}
        self.test_results = []
    
    async def run_performance_test(self, test_name: str, test_func: Callable,
                                 iterations: int = 10, *args, **kwargs) -> Dict[str, Any]:
        """运行性能测试"""
        self.logger.info(f"开始性能测试: {test_name}")
        
        durations = []
        errors = []
        
        for i in range(iterations):
            start_time = time.time()
            try:
                if asyncio.iscoroutinefunction(test_func):
                    await test_func(*args, **kwargs)
                else:
                    test_func(*args, **kwargs)
                
                duration = time.time() - start_time
                durations.append(duration)
                
            except Exception as e:
                errors.append(str(e))
                self.logger.error(f"性能测试迭代 {i+1} 失败: {e}")
        
        # 计算统计数据
        if durations:
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
        else:
            avg_duration = min_duration = max_duration = 0
        
        result = {
            'test_name': test_name,
            'iterations': iterations,
            'successful_iterations': len(durations),
            'failed_iterations': len(errors),
            'avg_duration': avg_duration,
            'min_duration': min_duration,
            'max_duration': max_duration,
            'durations': durations,
            'errors': errors
        }
        
        self.test_results.append(result)
        self.logger.info(f"性能测试完成: {test_name}", **result)
        
        return result
    
    def set_baseline(self, test_name: str, baseline_duration: float):
        """设置性能基线"""
        self.baseline_data[test_name] = baseline_duration
        self.logger.info(f"设置性能基线: {test_name} = {baseline_duration:.3f}s")
    
    def compare_with_baseline(self, test_name: str, current_duration: float) -> Dict[str, Any]:
        """与基线比较"""
        if test_name not in self.baseline_data:
            return {'error': '未找到基线数据'}
        
        baseline = self.baseline_data[test_name]
        improvement = (baseline - current_duration) / baseline * 100
        
        comparison = {
            'test_name': test_name,
            'baseline_duration': baseline,
            'current_duration': current_duration,
            'improvement_percent': improvement,
            'is_regression': improvement < -5  # 性能下降超过5%视为回归
        }
        
        self.logger.info(f"性能比较: {test_name}", **comparison)
        return comparison
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.test_results:
            return {'error': '没有测试结果'}
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['failed_iterations'] == 0])
        
        avg_durations = [r['avg_duration'] for r in self.test_results if r['avg_duration'] > 0]
        overall_avg = sum(avg_durations) / len(avg_durations) if avg_durations else 0
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests / total_tests * 100 if total_tests > 0 else 0,
                'overall_avg_duration': overall_avg
            },
            'test_results': self.test_results,
            'baselines': self.baseline_data
        }
        
        return report


class TestCoverageAnalyzer:
    """
    测试覆盖率分析器
    
    分析和报告测试覆盖率
    """
    
    def __init__(self):
        """初始化覆盖率分析器"""
        self.logger = get_structured_logger("CoverageAnalyzer")
        self.coverage_data = {}
    
    def analyze_coverage(self, source_dir: str) -> Dict[str, Any]:
        """分析测试覆盖率"""
        try:
            import coverage
            
            # 创建覆盖率对象
            cov = coverage.Coverage()
            cov.start()
            
            # 这里应该运行测试
            # 实际实现中会集成到测试运行器中
            
            cov.stop()
            cov.save()
            
            # 生成报告
            report_data = {}
            for filename in cov.get_data().measured_files():
                analysis = cov.analysis2(filename)
                report_data[filename] = {
                    'statements': len(analysis[1]),
                    'missing': len(analysis[3]),
                    'coverage_percent': (len(analysis[1]) - len(analysis[3])) / len(analysis[1]) * 100 if analysis[1] else 0
                }
            
            return report_data
            
        except ImportError:
            self.logger.warning("coverage 模块未安装，无法分析覆盖率")
            return {}
        except Exception as e:
            self.logger.error(f"覆盖率分析失败: {e}")
            return {}
    
    def generate_coverage_report(self, coverage_data: Dict[str, Any]) -> str:
        """生成覆盖率报告"""
        if not coverage_data:
            return "无覆盖率数据"
        
        total_statements = sum(data['statements'] for data in coverage_data.values())
        total_missing = sum(data['missing'] for data in coverage_data.values())
        overall_coverage = (total_statements - total_missing) / total_statements * 100 if total_statements > 0 else 0
        
        report = f"测试覆盖率报告\n"
        report += f"=" * 50 + "\n"
        report += f"总体覆盖率: {overall_coverage:.2f}%\n"
        report += f"总语句数: {total_statements}\n"
        report += f"未覆盖语句数: {total_missing}\n\n"
        
        report += "文件详情:\n"
        for filename, data in coverage_data.items():
            report += f"{filename}: {data['coverage_percent']:.2f}% ({data['statements'] - data['missing']}/{data['statements']})\n"
        
        return report


# 全局测试工具实例
_performance_runner = None
_coverage_analyzer = None


def get_performance_runner() -> PerformanceTestRunner:
    """获取性能测试运行器"""
    global _performance_runner
    if _performance_runner is None:
        _performance_runner = PerformanceTestRunner()
    return _performance_runner


def get_coverage_analyzer() -> TestCoverageAnalyzer:
    """获取覆盖率分析器"""
    global _coverage_analyzer
    if _coverage_analyzer is None:
        _coverage_analyzer = TestCoverageAnalyzer()
    return _coverage_analyzer
