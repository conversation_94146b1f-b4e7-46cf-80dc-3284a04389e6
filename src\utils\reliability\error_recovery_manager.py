#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
错误恢复管理器

提供自动错误恢复、重试机制和故障转移功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import time
import logging
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import random
import traceback

from ..logger import get_logger
from ..unified_types import TaskStatus, TaskType, UnifiedTaskResult

logger = get_logger("ErrorRecoveryManager")


class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    IMMEDIATE_RETRY = "immediate_retry"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    CIRCUIT_BREAKER = "circuit_breaker"
    FALLBACK = "fallback"
    NO_RECOVERY = "no_recovery"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 可忽略的错误
    MEDIUM = "medium"     # 需要重试的错误
    HIGH = "high"         # 需要降级的错误
    CRITICAL = "critical" # 需要立即处理的错误


@dataclass
class RecoveryConfig:
    """恢复配置"""
    strategy: RecoveryStrategy = RecoveryStrategy.EXPONENTIAL_BACKOFF
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    timeout: Optional[float] = None
    fallback_func: Optional[Callable] = None
    recovery_conditions: List[type] = field(default_factory=list)


@dataclass
class ErrorContext:
    """错误上下文信息"""
    error: Exception
    task_id: str
    task_type: TaskType
    attempt_count: int
    first_error_time: float
    last_error_time: float
    error_history: List[Exception] = field(default_factory=list)
    recovery_attempts: List[Dict] = field(default_factory=list)


class ErrorRecoveryManager:
    """
    错误恢复管理器
    
    提供智能的错误恢复、重试和降级机制
    """
    
    def __init__(self):
        """初始化错误恢复管理器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 错误上下文存储
        self._error_contexts: Dict[str, ErrorContext] = {}
        self._recovery_configs: Dict[TaskType, RecoveryConfig] = {}
        
        # 统计信息
        self._recovery_stats = {
            'total_errors': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'fallback_activations': 0
        }
        
        # 默认配置
        self._setup_default_configs()
        
        self.logger.info("错误恢复管理器初始化完成")
    
    def _setup_default_configs(self):
        """设置默认恢复配置"""
        # 文件扫描任务配置
        self._recovery_configs[TaskType.FILE_SCAN] = RecoveryConfig(
            strategy=RecoveryStrategy.EXPONENTIAL_BACKOFF,
            max_retries=3,
            base_delay=2.0,
            max_delay=30.0,
            recovery_conditions=[OSError, PermissionError]
        )
        
        # 数据库操作配置
        self._recovery_configs[TaskType.DATABASE_OPERATION] = RecoveryConfig(
            strategy=RecoveryStrategy.LINEAR_BACKOFF,
            max_retries=5,
            base_delay=1.0,
            max_delay=10.0,
            timeout=30.0
        )
        
        # 重复文件查找配置
        self._recovery_configs[TaskType.DUPLICATE_FIND] = RecoveryConfig(
            strategy=RecoveryStrategy.CIRCUIT_BREAKER,
            max_retries=2,
            base_delay=5.0,
            max_delay=60.0
        )
        
        # 通用任务配置
        self._recovery_configs[TaskType.GENERAL] = RecoveryConfig(
            strategy=RecoveryStrategy.EXPONENTIAL_BACKOFF,
            max_retries=3,
            base_delay=1.0,
            max_delay=30.0
        )
    
    def register_recovery_config(self, task_type: TaskType, config: RecoveryConfig):
        """注册任务类型的恢复配置"""
        self._recovery_configs[task_type] = config
        self.logger.info(f"注册恢复配置: {task_type.value} -> {config.strategy.value}")
    
    async def execute_with_recovery(self, 
                                   task_id: str,
                                   task_type: TaskType,
                                   func: Callable,
                                   *args, **kwargs) -> Any:
        """
        执行函数并提供错误恢复
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            Exception: 所有恢复尝试失败后抛出最后的异常
        """
        config = self._recovery_configs.get(task_type, self._recovery_configs[TaskType.GENERAL])
        
        # 初始化错误上下文
        if task_id not in self._error_contexts:
            self._error_contexts[task_id] = ErrorContext(
                error=None,
                task_id=task_id,
                task_type=task_type,
                attempt_count=0,
                first_error_time=0,
                last_error_time=0
            )
        
        context = self._error_contexts[task_id]
        
        for attempt in range(config.max_retries + 1):
            try:
                context.attempt_count = attempt + 1
                
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    if config.timeout:
                        result = await asyncio.wait_for(
                            func(*args, **kwargs), 
                            timeout=config.timeout
                        )
                    else:
                        result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 成功执行，清理错误上下文
                if task_id in self._error_contexts:
                    del self._error_contexts[task_id]
                
                if attempt > 0:
                    self._recovery_stats['successful_recoveries'] += 1
                    self.logger.info(f"任务恢复成功: {task_id}, 尝试次数: {attempt + 1}")
                
                return result
                
            except Exception as e:
                self._recovery_stats['total_errors'] += 1
                context.error = e
                context.error_history.append(e)
                context.last_error_time = time.time()
                
                if context.first_error_time == 0:
                    context.first_error_time = context.last_error_time
                
                self.logger.warning(f"任务执行失败: {task_id}, 尝试 {attempt + 1}/{config.max_retries + 1}, 错误: {e}")
                
                # 检查是否应该重试
                if not self._should_retry(e, config, attempt, config.max_retries):
                    break
                
                # 计算延迟时间
                delay = self._calculate_delay(config, attempt)
                
                # 记录恢复尝试
                context.recovery_attempts.append({
                    'attempt': attempt + 1,
                    'error': str(e),
                    'delay': delay,
                    'timestamp': time.time()
                })
                
                if attempt < config.max_retries:
                    self.logger.info(f"等待 {delay:.2f}s 后重试任务: {task_id}")
                    await asyncio.sleep(delay)
        
        # 所有重试失败，尝试降级处理
        if config.fallback_func:
            try:
                self.logger.info(f"执行降级处理: {task_id}")
                self._recovery_stats['fallback_activations'] += 1
                
                if asyncio.iscoroutinefunction(config.fallback_func):
                    return await config.fallback_func(*args, **kwargs)
                else:
                    return config.fallback_func(*args, **kwargs)
                    
            except Exception as fallback_error:
                self.logger.error(f"降级处理也失败: {task_id}, 错误: {fallback_error}")
        
        # 记录最终失败
        self._recovery_stats['failed_recoveries'] += 1
        self.logger.error(f"任务最终失败: {task_id}, 总尝试次数: {context.attempt_count}")
        
        # 抛出最后的异常
        raise context.error
    
    def _should_retry(self, error: Exception, config: RecoveryConfig, 
                     attempt: int, max_retries: int) -> bool:
        """判断是否应该重试"""
        # 检查是否超过最大重试次数
        if attempt >= max_retries:
            return False
        
        # 检查错误类型是否在恢复条件中
        if config.recovery_conditions:
            if not any(isinstance(error, exc_type) for exc_type in config.recovery_conditions):
                self.logger.debug(f"错误类型不在恢复条件中: {type(error).__name__}")
                return False
        
        # 检查特定的不可恢复错误
        non_recoverable_errors = [
            KeyboardInterrupt,
            SystemExit,
            asyncio.CancelledError
        ]
        
        if any(isinstance(error, exc_type) for exc_type in non_recoverable_errors):
            self.logger.debug(f"不可恢复的错误类型: {type(error).__name__}")
            return False
        
        return True
    
    def _calculate_delay(self, config: RecoveryConfig, attempt: int) -> float:
        """计算重试延迟时间"""
        if config.strategy == RecoveryStrategy.IMMEDIATE_RETRY:
            delay = 0
        elif config.strategy == RecoveryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * (attempt + 1)
        elif config.strategy == RecoveryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** attempt)
        else:
            delay = config.base_delay
        
        # 限制最大延迟
        delay = min(delay, config.max_delay)
        
        # 添加抖动
        if config.jitter:
            jitter_range = delay * 0.1  # 10% 抖动
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def get_error_context(self, task_id: str) -> Optional[ErrorContext]:
        """获取任务的错误上下文"""
        return self._error_contexts.get(task_id)
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """获取恢复统计信息"""
        return {
            **self._recovery_stats,
            'active_error_contexts': len(self._error_contexts),
            'success_rate': (
                self._recovery_stats['successful_recoveries'] / 
                max(1, self._recovery_stats['total_errors'])
            ) * 100
        }
    
    def clear_error_context(self, task_id: str):
        """清理任务的错误上下文"""
        if task_id in self._error_contexts:
            del self._error_contexts[task_id]
            self.logger.debug(f"清理错误上下文: {task_id}")
    
    def cleanup_old_contexts(self, max_age: float = 3600):
        """清理过期的错误上下文"""
        current_time = time.time()
        to_remove = []
        
        for task_id, context in self._error_contexts.items():
            if current_time - context.last_error_time > max_age:
                to_remove.append(task_id)
        
        for task_id in to_remove:
            del self._error_contexts[task_id]
        
        if to_remove:
            self.logger.info(f"清理过期错误上下文: {len(to_remove)}个")


# 全局错误恢复管理器实例
_error_recovery_manager = None


def get_error_recovery_manager() -> ErrorRecoveryManager:
    """获取全局错误恢复管理器实例"""
    global _error_recovery_manager
    if _error_recovery_manager is None:
        _error_recovery_manager = ErrorRecoveryManager()
    return _error_recovery_manager


# 装饰器函数
def with_error_recovery(task_type: TaskType = TaskType.GENERAL):
    """错误恢复装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            manager = get_error_recovery_manager()
            task_id = kwargs.get('task_id', f"auto_{int(time.time())}")
            return await manager.execute_with_recovery(
                task_id, task_type, func, *args, **kwargs
            )
        return wrapper
    return decorator


class DegradationManager:
    """
    降级管理器

    在系统出现故障时提供服务降级功能
    """

    def __init__(self):
        """初始化降级管理器"""
        self.logger = get_logger("DegradationManager")

        # 降级策略配置
        self._degradation_configs = {}

        # 降级状态
        self._degraded_services = set()

        # 降级历史
        self._degradation_history = []

        # 服务健康状态
        self._service_health = {}

        self._setup_default_degradations()

        self.logger.info("降级管理器初始化完成")

    def _setup_default_degradations(self):
        """设置默认降级策略"""
        # 文件扫描降级：跳过大文件
        self.register_degradation(
            "file_scan",
            self._file_scan_degradation,
            trigger_conditions=["high_memory_usage", "slow_response"]
        )

        # 重复文件检测降级：使用快速哈希
        self.register_degradation(
            "duplicate_detection",
            self._duplicate_detection_degradation,
            trigger_conditions=["high_cpu_usage", "timeout"]
        )

        # 数据库操作降级：使用缓存
        self.register_degradation(
            "database_operation",
            self._database_operation_degradation,
            trigger_conditions=["database_error", "connection_timeout"]
        )

    def register_degradation(self, service_name: str, degradation_func: Callable,
                           trigger_conditions: List[str] = None):
        """
        注册降级策略

        Args:
            service_name: 服务名称
            degradation_func: 降级函数
            trigger_conditions: 触发条件列表
        """
        self._degradation_configs[service_name] = {
            'function': degradation_func,
            'trigger_conditions': trigger_conditions or [],
            'enabled': True
        }
        self.logger.info(f"注册降级策略: {service_name}")

    async def check_degradation_triggers(self, service_name: str,
                                       context: Dict[str, Any]) -> bool:
        """
        检查是否应该触发降级

        Args:
            service_name: 服务名称
            context: 上下文信息

        Returns:
            是否应该降级
        """
        if service_name not in self._degradation_configs:
            return False

        config = self._degradation_configs[service_name]
        if not config['enabled']:
            return False

        # 检查触发条件
        for condition in config['trigger_conditions']:
            if await self._evaluate_condition(condition, context):
                self.logger.warning(f"触发降级条件: {service_name} -> {condition}")
                return True

        return False

    async def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估降级条件"""
        if condition == "high_memory_usage":
            memory_usage = context.get('memory_usage_percent', 0)
            return memory_usage > 85

        elif condition == "high_cpu_usage":
            cpu_usage = context.get('cpu_usage_percent', 0)
            return cpu_usage > 90

        elif condition == "slow_response":
            response_time = context.get('response_time', 0)
            return response_time > 5.0  # 5秒

        elif condition == "timeout":
            return context.get('timeout_occurred', False)

        elif condition == "database_error":
            return context.get('database_error', False)

        elif condition == "connection_timeout":
            return context.get('connection_timeout', False)

        return False

    async def execute_with_degradation(self, service_name: str,
                                     normal_func: Callable,
                                     context: Dict[str, Any],
                                     *args, **kwargs) -> Any:
        """
        执行服务，必要时进行降级

        Args:
            service_name: 服务名称
            normal_func: 正常执行函数
            context: 执行上下文
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            执行结果
        """
        # 检查是否需要降级
        should_degrade = await self.check_degradation_triggers(service_name, context)

        if should_degrade or service_name in self._degraded_services:
            # 执行降级逻辑
            return await self._execute_degraded_service(
                service_name, context, *args, **kwargs
            )
        else:
            # 执行正常逻辑
            try:
                return await normal_func(*args, **kwargs)
            except Exception as e:
                # 正常执行失败，尝试降级
                self.logger.warning(f"正常执行失败，尝试降级: {service_name}, 错误: {e}")
                context['execution_error'] = str(e)
                return await self._execute_degraded_service(
                    service_name, context, *args, **kwargs
                )

    async def _execute_degraded_service(self, service_name: str,
                                      context: Dict[str, Any],
                                      *args, **kwargs) -> Any:
        """执行降级服务"""
        if service_name not in self._degradation_configs:
            raise RuntimeError(f"未找到降级策略: {service_name}")

        config = self._degradation_configs[service_name]
        degradation_func = config['function']

        # 标记服务为降级状态
        if service_name not in self._degraded_services:
            self._degraded_services.add(service_name)
            self._record_degradation(service_name, "activated", context)

        try:
            if asyncio.iscoroutinefunction(degradation_func):
                return await degradation_func(context, *args, **kwargs)
            else:
                return degradation_func(context, *args, **kwargs)
        except Exception as e:
            self.logger.error(f"降级执行也失败: {service_name}, 错误: {e}")
            raise

    def _record_degradation(self, service_name: str, action: str,
                          context: Dict[str, Any]):
        """记录降级历史"""
        record = {
            'service_name': service_name,
            'action': action,
            'timestamp': time.time(),
            'context': context.copy()
        }

        self._degradation_history.append(record)

        # 限制历史记录数量
        if len(self._degradation_history) > 1000:
            self._degradation_history = self._degradation_history[-500:]

        self.logger.info(f"记录降级事件: {service_name} {action}")

    async def recover_service(self, service_name: str):
        """恢复服务到正常状态"""
        if service_name in self._degraded_services:
            self._degraded_services.remove(service_name)
            self._record_degradation(service_name, "recovered", {})
            self.logger.info(f"服务恢复正常: {service_name}")

    # 默认降级策略实现
    async def _file_scan_degradation(self, context: Dict[str, Any],
                                   *args, **kwargs) -> Any:
        """文件扫描降级策略：跳过大文件"""
        self.logger.info("执行文件扫描降级策略：跳过大文件")

        # 模拟降级逻辑：只扫描小于100MB的文件
        max_file_size = 100 * 1024 * 1024  # 100MB

        # 返回降级结果
        return {
            'status': 'degraded',
            'message': '由于系统负载过高，跳过大文件扫描',
            'max_file_size': max_file_size,
            'scanned_files': 0  # 实际实现中会返回扫描的文件数
        }

    async def _duplicate_detection_degradation(self, context: Dict[str, Any],
                                             *args, **kwargs) -> Any:
        """重复文件检测降级策略：使用快速哈希"""
        self.logger.info("执行重复文件检测降级策略：使用快速哈希")

        return {
            'status': 'degraded',
            'message': '使用快速哈希算法进行重复文件检测',
            'algorithm': 'md5_partial',  # 使用部分文件内容的MD5
            'accuracy': 'reduced'
        }

    async def _database_operation_degradation(self, context: Dict[str, Any],
                                            *args, **kwargs) -> Any:
        """数据库操作降级策略：使用缓存"""
        self.logger.info("执行数据库操作降级策略：使用缓存")

        return {
            'status': 'degraded',
            'message': '数据库不可用，使用缓存数据',
            'data_source': 'cache',
            'freshness': 'stale'
        }

    def get_degradation_status(self) -> Dict[str, Any]:
        """获取降级状态"""
        return {
            'degraded_services': list(self._degraded_services),
            'total_degradations': len(self._degradation_history),
            'available_strategies': list(self._degradation_configs.keys())
        }

    def get_degradation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取降级历史"""
        return self._degradation_history[-limit:]


# 全局降级管理器实例
_degradation_manager = None


def get_degradation_manager() -> DegradationManager:
    """获取全局降级管理器实例"""
    global _degradation_manager
    if _degradation_manager is None:
        _degradation_manager = DegradationManager()
    return _degradation_manager


# 降级装饰器
def with_degradation(service_name: str):
    """降级装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            manager = get_degradation_manager()
            context = kwargs.pop('degradation_context', {})
            return await manager.execute_with_degradation(
                service_name, func, context, *args, **kwargs
            )
        return wrapper
    return decorator
