#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
响应时间优化器

提供缓存、预加载、延迟加载和响应时间优化功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import time
import threading
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import pickle
import weakref
from functools import wraps, lru_cache

from ..logger import get_logger

logger = get_logger("ResponseOptimizer")


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    ADAPTIVE = "adaptive"


class PreloadStrategy(Enum):
    """预加载策略枚举"""
    EAGER = "eager"
    LAZY = "lazy"
    PREDICTIVE = "predictive"
    SCHEDULED = "scheduled"


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None
    size: int = 0
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    @property
    def age(self) -> float:
        """缓存年龄"""
        return time.time() - self.created_time
    
    def touch(self):
        """更新访问时间"""
        self.last_accessed = time.time()
        self.access_count += 1


class SmartCache:
    """
    智能缓存
    
    支持多种缓存策略和自动优化
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None,
                 strategy: CacheStrategy = CacheStrategy.ADAPTIVE):
        """初始化智能缓存"""
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.strategy = strategy
        
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        
        # 自适应参数
        self._access_patterns: Dict[str, List[float]] = {}
        self._prediction_accuracy = 0.0
        
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info(f"智能缓存初始化: 最大大小={max_size}, 策略={strategy.value}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self.misses += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired:
                del self._cache[key]
                self.misses += 1
                return None
            
            # 更新访问信息
            entry.touch()
            self.hits += 1
            
            # 记录访问模式
            self._record_access_pattern(key)
            
            return entry.value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """存储缓存值"""
        with self._lock:
            # 计算值大小
            try:
                size = len(pickle.dumps(value))
            except:
                size = 1  # 默认大小
            
            # 检查是否需要清理空间
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_entries(1)
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_time=time.time(),
                last_accessed=time.time(),
                ttl=ttl or self.default_ttl,
                size=size
            )
            
            self._cache[key] = entry
            return True
    
    def invalidate(self, key: str) -> bool:
        """使缓存失效"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.hits = 0
            self.misses = 0
            self.evictions = 0
    
    def _evict_entries(self, count: int):
        """驱逐缓存条目"""
        if not self._cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            self._evict_lru(count)
        elif self.strategy == CacheStrategy.LFU:
            self._evict_lfu(count)
        elif self.strategy == CacheStrategy.TTL:
            self._evict_expired(count)
        elif self.strategy == CacheStrategy.ADAPTIVE:
            self._evict_adaptive(count)
    
    def _evict_lru(self, count: int):
        """LRU驱逐"""
        sorted_entries = sorted(self._cache.items(), 
                               key=lambda x: x[1].last_accessed)
        
        for i in range(min(count, len(sorted_entries))):
            key = sorted_entries[i][0]
            del self._cache[key]
            self.evictions += 1
    
    def _evict_lfu(self, count: int):
        """LFU驱逐"""
        sorted_entries = sorted(self._cache.items(),
                               key=lambda x: x[1].access_count)
        
        for i in range(min(count, len(sorted_entries))):
            key = sorted_entries[i][0]
            del self._cache[key]
            self.evictions += 1
    
    def _evict_expired(self, count: int):
        """驱逐过期条目"""
        expired_keys = [key for key, entry in self._cache.items() if entry.is_expired]
        
        for key in expired_keys[:count]:
            del self._cache[key]
            self.evictions += 1
    
    def _evict_adaptive(self, count: int):
        """自适应驱逐"""
        # 综合考虑访问频率、时间和预测
        scored_entries = []
        
        for key, entry in self._cache.items():
            # 计算综合评分
            recency_score = 1.0 / (entry.age + 1)
            frequency_score = entry.access_count / 100.0
            prediction_score = self._predict_future_access(key)
            
            total_score = recency_score + frequency_score + prediction_score
            scored_entries.append((key, total_score))
        
        # 按评分排序，驱逐评分最低的
        scored_entries.sort(key=lambda x: x[1])
        
        for i in range(min(count, len(scored_entries))):
            key = scored_entries[i][0]
            del self._cache[key]
            self.evictions += 1
    
    def _record_access_pattern(self, key: str):
        """记录访问模式"""
        if key not in self._access_patterns:
            self._access_patterns[key] = []
        
        self._access_patterns[key].append(time.time())
        
        # 限制历史记录长度
        if len(self._access_patterns[key]) > 100:
            self._access_patterns[key] = self._access_patterns[key][-50:]
    
    def _predict_future_access(self, key: str) -> float:
        """预测未来访问概率"""
        if key not in self._access_patterns:
            return 0.0
        
        accesses = self._access_patterns[key]
        if len(accesses) < 2:
            return 0.0
        
        # 简单的时间间隔预测
        intervals = [accesses[i] - accesses[i-1] for i in range(1, len(accesses))]
        avg_interval = sum(intervals) / len(intervals)
        
        time_since_last = time.time() - accesses[-1]
        
        # 如果接近平均间隔，预测概率较高
        if time_since_last < avg_interval * 1.5:
            return 1.0 - (time_since_last / (avg_interval * 1.5))
        
        return 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate,
                'evictions': self.evictions,
                'strategy': self.strategy.value
            }


class PreloadManager:
    """
    预加载管理器
    
    智能预加载数据以减少响应时间
    """
    
    def __init__(self, strategy: PreloadStrategy = PreloadStrategy.PREDICTIVE):
        """初始化预加载管理器"""
        self.strategy = strategy
        self.logger = get_logger(self.__class__.__name__)
        
        # 预加载任务
        self._preload_tasks: Dict[str, asyncio.Task] = {}
        self._preload_cache = SmartCache(max_size=500, default_ttl=300)  # 5分钟TTL
        
        # 访问模式分析
        self._access_sequences: List[str] = []
        self._sequence_patterns: Dict[str, List[str]] = {}
        
        # 调度器
        self._scheduler_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self):
        """启动预加载管理器"""
        if self._running:
            return
        
        self._running = True
        if self.strategy == PreloadStrategy.SCHEDULED:
            self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        self.logger.info(f"预加载管理器已启动，策略: {self.strategy.value}")
    
    async def stop(self):
        """停止预加载管理器"""
        self._running = False
        
        # 取消所有预加载任务
        for task in self._preload_tasks.values():
            task.cancel()
        
        if self._scheduler_task:
            self._scheduler_task.cancel()
        
        self.logger.info("预加载管理器已停止")
    
    async def preload(self, key: str, loader: Callable[[], Any], 
                     priority: int = 1) -> bool:
        """预加载数据"""
        if key in self._preload_tasks:
            return False  # 已在预加载中
        
        # 检查缓存中是否已存在
        if self._preload_cache.get(key) is not None:
            return True
        
        # 创建预加载任务
        task = asyncio.create_task(self._execute_preload(key, loader))
        self._preload_tasks[key] = task
        
        self.logger.debug(f"开始预加载: {key}")
        return True
    
    async def _execute_preload(self, key: str, loader: Callable[[], Any]):
        """执行预加载"""
        try:
            if asyncio.iscoroutinefunction(loader):
                result = await loader()
            else:
                result = loader()
            
            # 存储到预加载缓存
            self._preload_cache.put(key, result)
            self.logger.debug(f"预加载完成: {key}")
            
        except Exception as e:
            self.logger.warning(f"预加载失败: {key}, 错误: {e}")
        
        finally:
            # 清理任务
            if key in self._preload_tasks:
                del self._preload_tasks[key]
    
    def get_preloaded(self, key: str) -> Optional[Any]:
        """获取预加载的数据"""
        return self._preload_cache.get(key)
    
    def record_access(self, key: str):
        """记录访问，用于模式分析"""
        self._access_sequences.append(key)
        
        # 限制序列长度
        if len(self._access_sequences) > 1000:
            self._access_sequences = self._access_sequences[-500:]
        
        # 分析序列模式
        self._analyze_access_patterns()
    
    def _analyze_access_patterns(self):
        """分析访问模式"""
        if len(self._access_sequences) < 3:
            return
        
        # 分析最近的访问序列
        recent_sequence = self._access_sequences[-10:]
        
        for i in range(len(recent_sequence) - 2):
            pattern = tuple(recent_sequence[i:i+2])
            next_item = recent_sequence[i+2]
            
            pattern_key = f"{pattern[0]}->{pattern[1]}"
            if pattern_key not in self._sequence_patterns:
                self._sequence_patterns[pattern_key] = []
            
            if next_item not in self._sequence_patterns[pattern_key]:
                self._sequence_patterns[pattern_key].append(next_item)
    
    def predict_next_access(self, current_key: str, previous_key: str = None) -> List[str]:
        """预测下一个可能的访问"""
        if previous_key is None:
            return []
        
        pattern_key = f"{previous_key}->{current_key}"
        return self._sequence_patterns.get(pattern_key, [])
    
    async def _scheduler_loop(self):
        """调度循环"""
        while self._running:
            try:
                # 定期清理过期的预加载数据
                await self._cleanup_expired_preloads()
                
                # 基于模式预测进行预加载
                await self._predictive_preload()
                
                await asyncio.sleep(60)  # 每分钟执行一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"调度循环出错: {e}")
                await asyncio.sleep(10)
    
    async def _cleanup_expired_preloads(self):
        """清理过期的预加载数据"""
        # SmartCache会自动处理过期数据
        pass
    
    async def _predictive_preload(self):
        """基于预测的预加载"""
        if len(self._access_sequences) < 2:
            return
        
        # 获取最近的访问
        recent_accesses = self._access_sequences[-2:]
        predictions = self.predict_next_access(recent_accesses[-1], 
                                             recent_accesses[-2] if len(recent_accesses) > 1 else None)
        
        # 对预测的项目进行预加载（需要具体的加载器实现）
        for predicted_key in predictions[:3]:  # 只预加载前3个预测
            if predicted_key not in self._preload_tasks:
                self.logger.debug(f"预测性预加载: {predicted_key}")
                # 这里需要根据具体业务实现加载器
    
    def get_stats(self) -> Dict[str, Any]:
        """获取预加载统计"""
        return {
            'strategy': self.strategy.value,
            'active_preloads': len(self._preload_tasks),
            'cache_stats': self._preload_cache.get_stats(),
            'access_sequence_length': len(self._access_sequences),
            'pattern_count': len(self._sequence_patterns)
        }


class ResponseTimeOptimizer:
    """
    响应时间优化器主类
    
    协调缓存、预加载和其他优化策略
    """
    
    def __init__(self):
        """初始化响应时间优化器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 组件初始化
        self.cache = SmartCache(max_size=2000, default_ttl=600)  # 10分钟TTL
        self.preload_manager = PreloadManager()
        
        # 响应时间监控
        self._response_times: Dict[str, List[float]] = {}
        self._optimization_targets: Dict[str, float] = {}
        
        # 优化策略配置
        self._strategies = {
            'caching': True,
            'preloading': True,
            'lazy_loading': True,
            'compression': False
        }
        
        self.logger.info("响应时间优化器初始化完成")
    
    async def start(self):
        """启动优化器"""
        await self.preload_manager.start()
        self.logger.info("响应时间优化器已启动")
    
    async def stop(self):
        """停止优化器"""
        await self.preload_manager.stop()
        self.logger.info("响应时间优化器已停止")
    
    def cached_call(self, cache_key: str = None, ttl: Optional[float] = None):
        """缓存调用装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                if cache_key:
                    key = cache_key
                else:
                    key = self._generate_cache_key(func, args, kwargs)
                
                # 尝试从缓存获取
                cached_result = self.cache.get(key)
                if cached_result is not None:
                    return cached_result
                
                # 记录开始时间
                start_time = time.time()
                
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 记录响应时间
                response_time = time.time() - start_time
                self._record_response_time(func.__name__, response_time)
                
                # 存储到缓存
                self.cache.put(key, result, ttl)
                
                return result
            
            return wrapper
        return decorator
    
    def _generate_cache_key(self, func: Callable, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 创建函数签名的哈希
        func_name = f"{func.__module__}.{func.__name__}"
        
        # 序列化参数
        try:
            args_str = str(args) + str(sorted(kwargs.items()))
            key_hash = hashlib.md5(args_str.encode()).hexdigest()
            return f"{func_name}:{key_hash}"
        except:
            # 如果参数不能序列化，使用函数名和参数数量
            return f"{func_name}:args{len(args)}_kwargs{len(kwargs)}"
    
    def _record_response_time(self, func_name: str, response_time: float):
        """记录响应时间"""
        if func_name not in self._response_times:
            self._response_times[func_name] = []
        
        self._response_times[func_name].append(response_time)
        
        # 限制历史记录
        if len(self._response_times[func_name]) > 100:
            self._response_times[func_name] = self._response_times[func_name][-50:]
    
    def set_optimization_target(self, func_name: str, target_time: float):
        """设置优化目标"""
        self._optimization_targets[func_name] = target_time
        self.logger.info(f"设置优化目标: {func_name} < {target_time:.3f}s")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {
            'cache_stats': self.cache.get_stats(),
            'preload_stats': self.preload_manager.get_stats(),
            'response_times': {},
            'optimization_status': {}
        }
        
        # 分析响应时间
        for func_name, times in self._response_times.items():
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                
                report['response_times'][func_name] = {
                    'avg': avg_time,
                    'min': min_time,
                    'max': max_time,
                    'count': len(times)
                }
                
                # 检查是否达到优化目标
                if func_name in self._optimization_targets:
                    target = self._optimization_targets[func_name]
                    report['optimization_status'][func_name] = {
                        'target': target,
                        'current_avg': avg_time,
                        'achieved': avg_time <= target,
                        'improvement_needed': max(0, avg_time - target)
                    }
        
        return report


# 全局响应时间优化器实例
_response_optimizer = None
_optimizer_lock = threading.Lock()


def get_response_optimizer() -> ResponseTimeOptimizer:
    """获取全局响应时间优化器实例"""
    global _response_optimizer
    with _optimizer_lock:
        if _response_optimizer is None:
            _response_optimizer = ResponseTimeOptimizer()
        return _response_optimizer


# 装饰器函数
def optimize_response_time(cache_key: str = None, ttl: Optional[float] = None):
    """响应时间优化装饰器"""
    def decorator(func):
        optimizer = get_response_optimizer()
        return optimizer.cached_call(cache_key, ttl)(func)
    return decorator


class LazyLoader:
    """
    延迟加载器

    按需加载资源以减少初始化时间
    """

    def __init__(self):
        """初始化延迟加载器"""
        self._loaders: Dict[str, Callable] = {}
        self._loaded_items: Dict[str, Any] = {}
        self._loading_locks: Dict[str, asyncio.Lock] = {}
        self.logger = get_logger(self.__class__.__name__)

    def register_loader(self, key: str, loader: Callable[[], Any]):
        """注册加载器"""
        self._loaders[key] = loader
        self._loading_locks[key] = asyncio.Lock()
        self.logger.debug(f"注册延迟加载器: {key}")

    async def get(self, key: str) -> Any:
        """获取项目（延迟加载）"""
        # 如果已加载，直接返回
        if key in self._loaded_items:
            return self._loaded_items[key]

        # 如果没有加载器，返回None
        if key not in self._loaders:
            return None

        # 使用锁确保只加载一次
        async with self._loading_locks[key]:
            # 双重检查
            if key in self._loaded_items:
                return self._loaded_items[key]

            # 执行加载
            loader = self._loaders[key]
            try:
                if asyncio.iscoroutinefunction(loader):
                    item = await loader()
                else:
                    item = loader()

                self._loaded_items[key] = item
                self.logger.debug(f"延迟加载完成: {key}")
                return item

            except Exception as e:
                self.logger.error(f"延迟加载失败: {key}, 错误: {e}")
                raise

    def is_loaded(self, key: str) -> bool:
        """检查是否已加载"""
        return key in self._loaded_items

    def unload(self, key: str):
        """卸载项目"""
        if key in self._loaded_items:
            del self._loaded_items[key]
            self.logger.debug(f"卸载项目: {key}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'registered_loaders': len(self._loaders),
            'loaded_items': len(self._loaded_items),
            'load_ratio': len(self._loaded_items) / max(1, len(self._loaders)) * 100
        }


# 全局延迟加载器实例
_lazy_loader = None


def get_lazy_loader() -> LazyLoader:
    """获取全局延迟加载器实例"""
    global _lazy_loader
    if _lazy_loader is None:
        _lazy_loader = LazyLoader()
    return _lazy_loader


def lazy_load(key: str):
    """延迟加载装饰器"""
    def decorator(func):
        loader = get_lazy_loader()
        loader.register_loader(key, func)

        async def wrapper():
            return await loader.get(key)

        return wrapper
    return decorator
