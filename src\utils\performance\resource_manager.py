#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
资源管理器

提供资源池管理、动态扩缩容和资源利用率优化功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import threading
import time
import psutil
from typing import Dict, List, Optional, Any, Callable, Union, Generic, TypeVar
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import weakref

from ..logger import get_logger

logger = get_logger("ResourceManager")

T = TypeVar('T')


class ResourceType(Enum):
    """资源类型枚举"""
    CONNECTION = "connection"
    THREAD = "thread"
    PROCESS = "process"
    MEMORY = "memory"
    FILE_HANDLE = "file_handle"
    CUSTOM = "custom"


class ScalingPolicy(Enum):
    """扩缩容策略枚举"""
    FIXED = "fixed"
    DYNAMIC = "dynamic"
    PREDICTIVE = "predictive"
    REACTIVE = "reactive"


@dataclass
class ResourceMetrics:
    """资源指标"""
    resource_type: ResourceType
    total_capacity: int
    active_count: int
    idle_count: int
    utilization_rate: float
    avg_wait_time: float
    peak_usage: int
    timestamp: float = field(default_factory=time.time)
    
    @property
    def available_count(self) -> int:
        """可用资源数量"""
        return self.total_capacity - self.active_count
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'resource_type': self.resource_type.value,
            'total_capacity': self.total_capacity,
            'active_count': self.active_count,
            'idle_count': self.idle_count,
            'available_count': self.available_count,
            'utilization_rate': self.utilization_rate,
            'avg_wait_time': self.avg_wait_time,
            'peak_usage': self.peak_usage,
            'timestamp': self.timestamp
        }


class ResourcePool(Generic[T]):
    """
    通用资源池
    
    管理可重用资源的生命周期
    """
    
    def __init__(self, resource_type: ResourceType, factory: Callable[[], T],
                 min_size: int = 1, max_size: int = 10, 
                 idle_timeout: float = 300.0,
                 cleanup_func: Optional[Callable[[T], None]] = None):
        """
        初始化资源池
        
        Args:
            resource_type: 资源类型
            factory: 资源创建工厂函数
            min_size: 最小池大小
            max_size: 最大池大小
            idle_timeout: 空闲超时时间（秒）
            cleanup_func: 资源清理函数
        """
        self.resource_type = resource_type
        self.factory = factory
        self.min_size = min_size
        self.max_size = max_size
        self.idle_timeout = idle_timeout
        self.cleanup_func = cleanup_func
        
        # 资源存储
        self._available_resources: List[Tuple[T, float]] = []  # (resource, last_used_time)
        self._active_resources: Dict[int, T] = {}  # id -> resource
        self._resource_counter = 0
        
        # 同步控制
        self._lock = asyncio.Lock()
        self._condition = asyncio.Condition(self._lock)
        
        # 统计信息
        self.created_count = 0
        self.destroyed_count = 0
        self.acquired_count = 0
        self.released_count = 0
        self.wait_times: List[float] = []
        
        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        self.logger = get_logger(f"ResourcePool.{resource_type.value}")
        self.logger.info(f"资源池初始化: {resource_type.value}, 大小范围: {min_size}-{max_size}")
    
    async def start(self):
        """启动资源池"""
        if self._running:
            return
        
        self._running = True
        
        # 创建最小数量的资源
        async with self._lock:
            for _ in range(self.min_size):
                resource = await self._create_resource()
                self._available_resources.append((resource, time.time()))
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.logger.info(f"资源池已启动: {self.resource_type.value}")
    
    async def stop(self):
        """停止资源池"""
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有资源
        async with self._lock:
            # 清理可用资源
            for resource, _ in self._available_resources:
                await self._destroy_resource(resource)
            self._available_resources.clear()
            
            # 清理活跃资源
            for resource in self._active_resources.values():
                await self._destroy_resource(resource)
            self._active_resources.clear()
        
        self.logger.info(f"资源池已停止: {self.resource_type.value}")
    
    async def acquire(self, timeout: float = 30.0) -> T:
        """获取资源"""
        start_time = time.time()
        
        async with self._condition:
            while True:
                # 尝试获取可用资源
                if self._available_resources:
                    resource, _ = self._available_resources.pop(0)
                    resource_id = self._resource_counter
                    self._resource_counter += 1
                    self._active_resources[resource_id] = resource
                    
                    self.acquired_count += 1
                    wait_time = time.time() - start_time
                    self.wait_times.append(wait_time)
                    
                    # 限制等待时间历史记录
                    if len(self.wait_times) > 100:
                        self.wait_times = self.wait_times[-50:]
                    
                    self.logger.debug(f"获取资源: {resource_id}, 等待时间: {wait_time:.3f}s")
                    return resource
                
                # 如果可以创建新资源
                total_resources = len(self._available_resources) + len(self._active_resources)
                if total_resources < self.max_size:
                    resource = await self._create_resource()
                    resource_id = self._resource_counter
                    self._resource_counter += 1
                    self._active_resources[resource_id] = resource
                    
                    self.acquired_count += 1
                    wait_time = time.time() - start_time
                    self.wait_times.append(wait_time)
                    
                    self.logger.debug(f"创建并获取资源: {resource_id}")
                    return resource
                
                # 检查超时
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    raise asyncio.TimeoutError(f"获取资源超时: {self.resource_type.value}")
                
                # 等待资源释放
                remaining_timeout = timeout - elapsed
                try:
                    await asyncio.wait_for(self._condition.wait(), timeout=remaining_timeout)
                except asyncio.TimeoutError:
                    raise asyncio.TimeoutError(f"获取资源超时: {self.resource_type.value}")
    
    async def release(self, resource: T):
        """释放资源"""
        async with self._condition:
            # 找到并移除活跃资源
            resource_id = None
            for rid, res in self._active_resources.items():
                if res is resource:
                    resource_id = rid
                    break
            
            if resource_id is not None:
                del self._active_resources[resource_id]
                self._available_resources.append((resource, time.time()))
                self.released_count += 1
                
                self.logger.debug(f"释放资源: {resource_id}")
                
                # 通知等待的协程
                self._condition.notify()
            else:
                self.logger.warning("尝试释放未知资源")
    
    async def _create_resource(self) -> T:
        """创建资源"""
        try:
            if asyncio.iscoroutinefunction(self.factory):
                resource = await self.factory()
            else:
                resource = self.factory()
            
            self.created_count += 1
            self.logger.debug(f"创建资源: {self.resource_type.value}")
            return resource
            
        except Exception as e:
            self.logger.error(f"创建资源失败: {self.resource_type.value}, 错误: {e}")
            raise
    
    async def _destroy_resource(self, resource: T):
        """销毁资源"""
        try:
            if self.cleanup_func:
                if asyncio.iscoroutinefunction(self.cleanup_func):
                    await self.cleanup_func(resource)
                else:
                    self.cleanup_func(resource)
            
            self.destroyed_count += 1
            self.logger.debug(f"销毁资源: {self.resource_type.value}")
            
        except Exception as e:
            self.logger.warning(f"销毁资源失败: {self.resource_type.value}, 错误: {e}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self._running:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                await self._cleanup_idle_resources()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理循环出错: {e}")
    
    async def _cleanup_idle_resources(self):
        """清理空闲资源"""
        current_time = time.time()
        
        async with self._lock:
            # 找出过期的空闲资源
            expired_resources = []
            remaining_resources = []
            
            for resource, last_used in self._available_resources:
                if current_time - last_used > self.idle_timeout:
                    expired_resources.append(resource)
                else:
                    remaining_resources.append((resource, last_used))
            
            # 确保不低于最小大小
            total_resources = len(remaining_resources) + len(self._active_resources)
            while len(expired_resources) > 0 and total_resources > self.min_size:
                resource = expired_resources.pop()
                await self._destroy_resource(resource)
                total_resources -= 1
            
            # 更新可用资源列表
            self._available_resources = remaining_resources + [(r, current_time) for r in expired_resources]
            
            if expired_resources:
                self.logger.debug(f"清理空闲资源: {len(expired_resources)}个")
    
    def get_metrics(self) -> ResourceMetrics:
        """获取资源指标"""
        total_capacity = len(self._available_resources) + len(self._active_resources)
        active_count = len(self._active_resources)
        idle_count = len(self._available_resources)
        
        utilization_rate = (active_count / max(1, total_capacity)) * 100
        avg_wait_time = sum(self.wait_times) / len(self.wait_times) if self.wait_times else 0
        peak_usage = max(active_count, getattr(self, '_peak_usage', 0))
        self._peak_usage = peak_usage
        
        return ResourceMetrics(
            resource_type=self.resource_type,
            total_capacity=total_capacity,
            active_count=active_count,
            idle_count=idle_count,
            utilization_rate=utilization_rate,
            avg_wait_time=avg_wait_time,
            peak_usage=peak_usage
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        metrics = self.get_metrics()
        
        return {
            **metrics.to_dict(),
            'created_count': self.created_count,
            'destroyed_count': self.destroyed_count,
            'acquired_count': self.acquired_count,
            'released_count': self.released_count,
            'min_size': self.min_size,
            'max_size': self.max_size,
            'idle_timeout': self.idle_timeout
        }


class DynamicResourceManager:
    """
    动态资源管理器
    
    根据负载自动调整资源池大小
    """
    
    def __init__(self):
        """初始化动态资源管理器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 资源池注册表
        self._pools: Dict[str, ResourcePool] = {}
        
        # 扩缩容策略
        self._scaling_policies: Dict[str, ScalingPolicy] = {}
        self._scaling_configs: Dict[str, Dict[str, Any]] = {}
        
        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 系统指标
        self._system_metrics_history: List[Dict[str, Any]] = []
        
        self.logger.info("动态资源管理器初始化完成")
    
    async def start(self):
        """启动资源管理器"""
        if self._running:
            return
        
        self._running = True
        
        # 启动所有资源池
        for pool in self._pools.values():
            await pool.start()
        
        # 启动监控任务
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        
        self.logger.info("动态资源管理器已启动")
    
    async def stop(self):
        """停止资源管理器"""
        self._running = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        # 停止所有资源池
        for pool in self._pools.values():
            await pool.stop()
        
        self.logger.info("动态资源管理器已停止")
    
    def register_pool(self, pool_name: str, pool: ResourcePool,
                     scaling_policy: ScalingPolicy = ScalingPolicy.DYNAMIC,
                     scaling_config: Dict[str, Any] = None):
        """注册资源池"""
        self._pools[pool_name] = pool
        self._scaling_policies[pool_name] = scaling_policy
        self._scaling_configs[pool_name] = scaling_config or {}
        
        self.logger.info(f"注册资源池: {pool_name}, 策略: {scaling_policy.value}")
    
    async def get_resource(self, pool_name: str, timeout: float = 30.0):
        """从指定池获取资源"""
        if pool_name not in self._pools:
            raise ValueError(f"未找到资源池: {pool_name}")
        
        return await self._pools[pool_name].acquire(timeout)
    
    async def release_resource(self, pool_name: str, resource):
        """释放资源到指定池"""
        if pool_name not in self._pools:
            raise ValueError(f"未找到资源池: {pool_name}")
        
        await self._pools[pool_name].release(resource)
    
    async def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                # 收集系统指标
                system_metrics = await self._collect_system_metrics()
                self._system_metrics_history.append(system_metrics)
                
                # 限制历史记录
                if len(self._system_metrics_history) > 100:
                    self._system_metrics_history = self._system_metrics_history[-50:]
                
                # 执行扩缩容决策
                await self._execute_scaling_decisions()
                
                await asyncio.sleep(30)  # 每30秒监控一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                await asyncio.sleep(10)
    
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 资源池指标
            pool_metrics = {}
            for pool_name, pool in self._pools.items():
                pool_metrics[pool_name] = pool.get_metrics().to_dict()
            
            return {
                'timestamp': time.time(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_mb': memory.available / 1024 / 1024,
                'pool_metrics': pool_metrics
            }
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {'timestamp': time.time(), 'error': str(e)}
    
    async def _execute_scaling_decisions(self):
        """执行扩缩容决策"""
        if not self._system_metrics_history:
            return
        
        current_metrics = self._system_metrics_history[-1]
        
        for pool_name, pool in self._pools.items():
            policy = self._scaling_policies.get(pool_name, ScalingPolicy.FIXED)
            
            if policy == ScalingPolicy.DYNAMIC:
                await self._dynamic_scaling(pool_name, pool, current_metrics)
            elif policy == ScalingPolicy.REACTIVE:
                await self._reactive_scaling(pool_name, pool, current_metrics)
            elif policy == ScalingPolicy.PREDICTIVE:
                await self._predictive_scaling(pool_name, pool, current_metrics)
    
    async def _dynamic_scaling(self, pool_name: str, pool: ResourcePool, 
                             metrics: Dict[str, Any]):
        """动态扩缩容"""
        pool_metrics = metrics.get('pool_metrics', {}).get(pool_name, {})
        utilization = pool_metrics.get('utilization_rate', 0)
        
        config = self._scaling_configs.get(pool_name, {})
        scale_up_threshold = config.get('scale_up_threshold', 80)
        scale_down_threshold = config.get('scale_down_threshold', 20)
        
        # 扩容决策
        if utilization > scale_up_threshold and pool.max_size > len(pool._available_resources) + len(pool._active_resources):
            self.logger.info(f"触发扩容: {pool_name}, 利用率: {utilization:.1f}%")
            # 这里可以实现具体的扩容逻辑
        
        # 缩容决策
        elif utilization < scale_down_threshold:
            self.logger.info(f"触发缩容: {pool_name}, 利用率: {utilization:.1f}%")
            # 这里可以实现具体的缩容逻辑
    
    async def _reactive_scaling(self, pool_name: str, pool: ResourcePool,
                              metrics: Dict[str, Any]):
        """响应式扩缩容"""
        pool_metrics = metrics.get('pool_metrics', {}).get(pool_name, {})
        avg_wait_time = pool_metrics.get('avg_wait_time', 0)
        
        config = self._scaling_configs.get(pool_name, {})
        max_wait_time = config.get('max_wait_time', 1.0)
        
        if avg_wait_time > max_wait_time:
            self.logger.info(f"响应式扩容: {pool_name}, 平均等待时间: {avg_wait_time:.3f}s")
    
    async def _predictive_scaling(self, pool_name: str, pool: ResourcePool,
                                metrics: Dict[str, Any]):
        """预测性扩缩容"""
        # 基于历史数据预测未来负载
        if len(self._system_metrics_history) < 10:
            return
        
        # 简单的趋势预测
        recent_utilizations = []
        for m in self._system_metrics_history[-10:]:
            pool_metrics = m.get('pool_metrics', {}).get(pool_name, {})
            utilization = pool_metrics.get('utilization_rate', 0)
            recent_utilizations.append(utilization)
        
        if len(recent_utilizations) >= 3:
            trend = recent_utilizations[-1] - recent_utilizations[-3]
            if trend > 10:  # 利用率上升趋势
                self.logger.info(f"预测性扩容: {pool_name}, 趋势: +{trend:.1f}%")
    
    def get_overall_metrics(self) -> Dict[str, Any]:
        """获取整体指标"""
        pool_stats = {}
        for pool_name, pool in self._pools.items():
            pool_stats[pool_name] = pool.get_stats()
        
        # 系统指标
        system_metrics = {}
        if self._system_metrics_history:
            latest = self._system_metrics_history[-1]
            system_metrics = {
                'cpu_percent': latest.get('cpu_percent', 0),
                'memory_percent': latest.get('memory_percent', 0),
                'memory_available_mb': latest.get('memory_available_mb', 0)
            }
        
        return {
            'pool_stats': pool_stats,
            'system_metrics': system_metrics,
            'total_pools': len(self._pools),
            'monitoring_active': self._running
        }


# 全局动态资源管理器实例
_resource_manager = None
_manager_lock = threading.Lock()


def get_resource_manager() -> DynamicResourceManager:
    """获取全局动态资源管理器实例"""
    global _resource_manager
    with _manager_lock:
        if _resource_manager is None:
            _resource_manager = DynamicResourceManager()
        return _resource_manager
